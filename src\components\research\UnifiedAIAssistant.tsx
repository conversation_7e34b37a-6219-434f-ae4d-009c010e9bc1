import React, { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Brain,
  Send,
  Replace,
  Plus,
  Eye,
  Loader2,
  Settings,
  Sparkles,
  ChevronDown,
  ChevronUp,
  Copy,
  Check,
  Wrench,
  Search,
  X,
  Minimize2,
  Maximize2,
  ExternalLink,
  Quote,
  Wand2,
  BookOpen,
  PenTool,
  AlignLeft,
  FileText,
  Lightbulb,
  CheckCircle,
  TrendingUp,
  Edit3,
  Zap
} from 'lucide-react';
import { toast } from 'sonner';
import { enhancedAIService, AI_MODELS, AIModel, RESEARCH_TOOLS } from './paper-generator/enhanced-ai.service';
import { tavilySearchService } from './research-search/services/tavily-search.service';
import './academic-interface.css';

interface UnifiedAIAssistantProps {
  onAIRequest: (prompt: string, text: string, mode: 'replace' | 'insert' | 'display') => void;
  selectedText: string;
  documentContent: string;
  aiLoading: boolean;
  aiResponse: string;
  className?: string;
  isVisible: boolean;
  onToggleVisibility: () => void;
}

export function UnifiedAIAssistant({
  onAIRequest,
  selectedText,
  documentContent,
  aiLoading,
  aiResponse,
  className = '',
  isVisible,
  onToggleVisibility
}: UnifiedAIAssistantProps) {
  const [prompt, setPrompt] = useState('');
  const [selectedMode, setSelectedMode] = useState<'replace' | 'insert' | 'display'>('display');
  const [selectedModel, setSelectedModel] = useState<string>(enhancedAIService.getDefaultModel());
  const [isExpanded, setIsExpanded] = useState(false);
  const [showModelSelector, setShowModelSelector] = useState(false);
  const [activeTab, setActiveTab] = useState<'chat' | 'tools' | 'search'>('chat');
  const [copied, setCopied] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);
  const [processingTool, setProcessingTool] = useState<string | null>(null);
  const [showOutput, setShowOutput] = useState(false);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = textareaRef.current.scrollHeight + 'px';
    }
  }, [prompt]);

  // Show output when AI response is available
  useEffect(() => {
    if (aiResponse && aiResponse.trim()) {
      setShowOutput(true);
    }
  }, [aiResponse]);

  // Quick enhancement tools
  const quickTools = [
    {
      id: 'clarity',
      name: 'Improve',
      icon: <Sparkles className="h-4 w-4" />,
      color: 'blue',
      description: 'Improve clarity and flow',
      requiresSelection: true
    },
    {
      id: 'grammar',
      name: 'Grammar',
      icon: <CheckCircle className="h-4 w-4" />,
      color: 'green',
      description: 'Fix grammar and style',
      requiresSelection: true
    },
    {
      id: 'academic',
      name: 'Academic',
      icon: <PenTool className="h-4 w-4" />,
      color: 'purple',
      description: 'Convert to academic tone',
      requiresSelection: true
    },
    {
      id: 'expand',
      name: 'Expand',
      icon: <AlignLeft className="h-4 w-4" />,
      color: 'indigo',
      description: 'Add more detail',
      requiresSelection: true
    }
  ];

  // Research and analysis tools
  const researchTools = [
    {
      id: 'cite',
      name: 'Add Citations',
      icon: <BookOpen className="h-4 w-4" />,
      color: 'emerald',
      description: 'Add relevant citations',
      requiresSelection: true
    },
    {
      id: 'research',
      name: 'Find Research',
      icon: <Search className="h-4 w-4" />,
      color: 'orange',
      description: 'Find supporting research',
      requiresSelection: false
    },
    {
      id: 'summarize',
      name: 'Summarize',
      icon: <FileText className="h-4 w-4" />,
      color: 'cyan',
      description: 'Create concise summary',
      requiresSelection: true
    },
    {
      id: 'suggest',
      name: 'Suggestions',
      icon: <Lightbulb className="h-4 w-4" />,
      color: 'yellow',
      description: 'Get improvement suggestions',
      requiresSelection: true
    },
    {
      id: 'humanize',
      name: 'Humanize',
      icon: <Wand2 className="h-4 w-4" />,
      color: 'pink',
      description: 'Make text sound more natural',
      requiresSelection: true
    },
    {
      id: 'enhance',
      name: 'Enhance',
      icon: <TrendingUp className="h-4 w-4" />,
      color: 'violet',
      description: 'Enhance with research',
      requiresSelection: true
    }
  ];

  // Action mode options
  const actionModes = [
    {
      id: 'replace',
      label: 'Replace',
      icon: <Replace className="h-3 w-3" />,
      description: 'Replace the selected text',
      disabled: !selectedText.trim()
    },
    {
      id: 'insert',
      label: 'Insert',
      icon: <Plus className="h-3 w-3" />,
      description: 'Insert at cursor position'
    },
    {
      id: 'display',
      label: 'Display',
      icon: <Eye className="h-3 w-3" />,
      description: 'Show in assistant'
    }
  ];

  // Handle tool execution
  const handleToolExecution = async (tool: any) => {
    if (tool.requiresSelection && !selectedText.trim()) {
      toast.error(`Please select text to use "${tool.name}"`);
      return;
    }

    if (aiLoading || processingTool) return;

    setProcessingTool(tool.id);

    try {
      let result: string;
      const context = selectedText.trim() || documentContent.slice(0, 1000);

      // Handle different tool types
      switch (tool.id) {
        case 'clarity':
        case 'grammar':
        case 'academic':
        case 'expand':
          result = await enhancedAIService.quickEnhance(
            selectedText,
            tool.id as 'clarity' | 'grammar' | 'academic' | 'expand'
          );
          break;
        case 'cite':
          result = await enhancedAIService.generateText(
            `Add relevant academic citations to this text. Provide proper in-text citations and suggest credible sources:\n\n"${selectedText}"`
          );
          break;
        case 'research':
          const searchQuery = selectedText.trim() || prompt || 'research topic';
          const searchResponse = await tavilySearchService.searchAcademic(searchQuery, {
            maxResults: 5,
            searchDepth: 'advanced',
            includeAnswer: true
          });

          const searchContext = searchResponse.results
            .map((result, index) => `[${index + 1}] ${result.title}\n${result.content}\nSource: ${result.url}`)
            .join('\n\n');

          result = await enhancedAIService.generateText(
            `Based on the following research findings, provide a comprehensive analysis:\n\n${searchContext}\n\nContext: ${context}`
          );
          break;
        case 'summarize':
          result = await enhancedAIService.generateText(
            `Create a concise, academic summary of this text while preserving key points:\n\n"${selectedText}"`
          );
          break;
        case 'suggest':
          result = await enhancedAIService.generateText(
            `Provide specific suggestions to improve this text for academic writing:\n\n"${selectedText}"`
          );
          break;
        case 'humanize':
          result = await enhancedAIService.generateText(
            `Please humanize the following academic text to make it sound more natural and human-written while maintaining academic quality:\n\n"${selectedText}"`
          );
          break;
        case 'enhance':
          result = await handleSearchEnhance();
          break;
        default:
          throw new Error(`Unknown tool: ${tool.id}`);
      }

      // Apply result based on selected mode
      onAIRequest('', result, selectedMode);
      toast.success(`${tool.name} applied successfully`);

    } catch (error: any) {
      console.error('Tool execution failed:', error);
      toast.error(`Failed to execute ${tool.name}: ${error.message}`);
    } finally {
      setProcessingTool(null);
    }
  };

  // Handle search enhance (similar to FloatingAIToolbar)
  const handleSearchEnhance = async (): Promise<string> => {
    try {
      const searchQuery = `${selectedText} academic research enhancement`;
      const searchResults = await tavilySearchService.searchAcademic(searchQuery, {
        maxResults: 5,
        searchDepth: 'advanced',
        includeAnswer: true
      });

      const formattedResults = searchResults.results
        .map(result => `Title: ${result.title}\nContent: ${result.content}\nURL: ${result.url}`)
        .join('\n\n');

      const context = documentContent ? `Document context: ${documentContent.slice(0, 500)}...\n\n` : '';

      return await enhancedAIService.generateText(
        `${context}Enhance the following text using current research findings:

Original text: "${selectedText}"

Recent research findings:
${formattedResults}

Please:
1. Enhance the text with current research insights
2. Add relevant supporting information
3. Suggest appropriate citations
4. Improve academic tone and clarity
5. Maintain the original meaning while strengthening with evidence

Provide an enhanced version that incorporates the research findings.`
      );
    } catch (error: any) {
      console.error('Search enhance failed:', error);
      return await enhancedAIService.generateText(
        `Enhance this text for academic writing:\n\n"${selectedText}"`
      );
    }
  };

  // Handle AI request with online search
  const handleAIRequestWithSearch = async () => {
    if (!prompt.trim() || aiLoading) return;

    try {
      setSearchLoading(true);
      
      // First, perform online search
      const searchQuery = selectedText.trim() 
        ? `${prompt} ${selectedText}` 
        : prompt;

      const searchResponse = await tavilySearchService.searchAcademic(searchQuery, {
        maxResults: 5,
        searchDepth: 'advanced',
        includeAnswer: true
      });

      setSearchResults(searchResponse.results);

      // Create enhanced prompt with search results
      const searchContext = searchResponse.results
        .map((result, index) => `[${index + 1}] ${result.title}\n${result.content}\nSource: ${result.url}`)
        .join('\n\n');

      const enhancedPrompt = `
Based on the following current research and information, please provide a comprehensive academic response to: "${prompt}"

SEARCH RESULTS:
${searchContext}

CONTEXT: ${selectedText ? `Selected text: "${selectedText}"` : 'General document context'}

REQUIREMENTS:
1. Write in formal academic style
2. Include proper in-text citations (Author, Year) format
3. Synthesize information from multiple sources
4. Provide clear, well-structured response
5. Include a reference list at the end
6. Ensure content is scholarly and well-referenced

Please provide a thorough, academic response with proper citations and references.
`;

      // Use the enhanced prompt for AI generation
      const context = selectedText.trim() || documentContent.slice(0, 1000);
      onAIRequest(enhancedPrompt, context, selectedMode);
      
      setPrompt('');
      toast.success('AI request with online research submitted');
    } catch (error: any) {
      console.error('AI request with search error:', error);
      toast.error(error.message || 'Failed to submit AI request');
    } finally {
      setSearchLoading(false);
    }
  };

  // Handle regular AI request
  const handleRegularAIRequest = async () => {
    if (!prompt.trim() || aiLoading) return;

    try {
      const context = selectedText.trim() || documentContent.slice(0, 1000);
      const fullPrompt = selectedText.trim() 
        ? `Context: "${selectedText}"\n\nRequest: ${prompt}`
        : prompt;

      onAIRequest(fullPrompt, context, selectedMode);
      setPrompt('');
      toast.success('AI request submitted');
    } catch (error: any) {
      console.error('AI request error:', error);
      toast.error(error.message || 'Failed to submit AI request');
    }
  };

  // Handle humanize text
  const handleHumanizeText = async () => {
    if (!selectedText.trim()) {
      toast.error('Please select text to humanize');
      return;
    }

    const humanizePrompt = `
Please humanize the following academic text to make it sound more natural and human-written while maintaining academic quality and tone:

ORIGINAL TEXT:
"${selectedText}"

HUMANIZATION REQUIREMENTS:
1. Maintain academic rigor and scholarly tone
2. Improve natural flow and readability
3. Vary sentence structure and length
4. Use more natural transitions between ideas
5. Reduce overly complex or robotic phrasing
6. Keep all factual content and citations intact
7. Ensure the text sounds like it was written by a knowledgeable human academic
8. Maintain formal academic style appropriate for scholarly work

Please provide the humanized version that sounds natural while preserving academic quality.
`;

    try {
      const result = await enhancedAIService.generateText(humanizePrompt, selectedModel);
      onAIRequest('', result, 'replace');
      toast.success('Text humanized successfully');
    } catch (error: any) {
      console.error('Humanize error:', error);
      toast.error(error.message || 'Failed to humanize text');
    }
  };

  // Handle Enter key submission
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault();
      if (activeTab === 'search') {
        handleAIRequestWithSearch();
      } else {
        handleRegularAIRequest();
      }
    }
  };

  // Copy AI response to clipboard
  const handleCopyResponse = async () => {
    if (!aiResponse) return;
    
    try {
      await navigator.clipboard.writeText(aiResponse);
      setCopied(true);
      toast.success('Response copied to clipboard');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy response');
    }
  };

  // Get available models
  const availableModels = AI_MODELS.filter(model => 
    enhancedAIService.hasValidApiKey() || model.provider === 'Google'
  );

  if (!isVisible) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={onToggleVisibility}
          className="h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg"
          title="Open AI Assistant"
        >
          <Brain className="h-6 w-6 text-white" />
        </Button>
      </div>
    );
  }

  return (
    <div className={`fixed bottom-6 right-6 z-50 ${className}`}>
      <Card className={`floating-ai-assistant bg-white shadow-2xl border-gray-200 transition-all duration-300 ${
        isMinimized ? 'w-80 h-16' : 'w-[420px] max-h-[85vh] h-[650px]'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-gradient-to-r from-blue-50 to-purple-50">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-full">
              <Brain className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 text-sm">AI Assistant</h3>
              {selectedText && (
                <Badge variant="secondary" className="text-xs mt-1">
                  {selectedText.length} chars selected
                </Badge>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsMinimized(!isMinimized)}
              className="h-8 w-8 p-0"
              title={isMinimized ? "Expand" : "Minimize"}
            >
              {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleVisibility}
              className="h-8 w-8 p-0"
              title="Close"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {!isMinimized && (
          <>
            {/* Tabs */}
            <div className="flex border-b">
              {[
                { id: 'chat', label: 'Chat', icon: <Brain className="h-4 w-4" /> },
                { id: 'tools', label: 'Tools', icon: <Wrench className="h-4 w-4" /> },
                { id: 'search', label: 'Research', icon: <Search className="h-4 w-4" /> }
              ].map((tab) => (
                <Button
                  key={tab.id}
                  variant={activeTab === tab.id ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setActiveTab(tab.id as 'chat' | 'tools' | 'search')}
                  className="flex-1 rounded-none h-10"
                >
                  {tab.icon}
                  <span className="ml-1 text-xs">{tab.label}</span>
                </Button>
              ))}
            </div>

            {/* Content */}
            <div className="flex-1 flex flex-col overflow-hidden">
              {/* Model Selector */}
              {showModelSelector && (
                <div className="p-3 bg-gray-50 border-b">
                  <label className="text-xs font-medium text-gray-700 mb-2 block">
                    AI Model
                  </label>
                  <Select value={selectedModel} onValueChange={setSelectedModel}>
                    <SelectTrigger className="w-full h-8">
                      <SelectValue placeholder="Select AI model" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableModels.map((model) => (
                        <SelectItem key={model.id} value={model.id}>
                          <div className="flex flex-col">
                            <span className="font-medium text-xs">{model.name}</span>
                            <span className="text-xs text-gray-500">{model.description}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {/* Chat Tab */}
              {activeTab === 'chat' && (
                <div className="flex-1 flex flex-col p-3 space-y-3">
                  {/* Prompt Input */}
                  <div className="space-y-2">
                    <Textarea
                      ref={textareaRef}
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder={selectedText 
                        ? "How can I help with the selected text?" 
                        : "Ask me anything about your writing..."
                      }
                      className="ai-prompt-input min-h-[80px] max-h-[120px] resize-none text-sm"
                      disabled={aiLoading}
                    />
                    
                    {/* Action Buttons */}
                    <div className="flex gap-1">
                      {actionModes.map((mode) => (
                        <Button
                          key={mode.id}
                          variant={selectedMode === mode.id ? "default" : "outline"}
                          size="sm"
                          onClick={() => setSelectedMode(mode.id as 'replace' | 'insert' | 'display')}
                          disabled={mode.disabled}
                          className="flex items-center gap-1 text-xs h-7"
                          title={mode.description}
                        >
                          {mode.icon}
                          {mode.label}
                        </Button>
                      ))}
                    </div>

                    {/* Submit Button */}
                    <Button
                      onClick={handleRegularAIRequest}
                      disabled={!prompt.trim() || aiLoading}
                      className="w-full h-8"
                      size="sm"
                    >
                      {aiLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <Send className="h-4 w-4 mr-2" />
                      )}
                      Send
                    </Button>
                  </div>

                  {/* Quick Actions */}
                  {selectedText && (
                    <div className="space-y-2">
                      <Separator />
                      <div className="text-xs font-medium text-gray-700">Quick Actions</div>
                      <div className="grid grid-cols-2 gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleHumanizeText}
                          className="h-8 text-xs"
                          disabled={aiLoading}
                        >
                          <Wand2 className="h-3 w-3 mr-1" />
                          Humanize
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setPrompt('Improve the clarity and flow of this text');
                            handleRegularAIRequest();
                          }}
                          className="h-8 text-xs"
                          disabled={aiLoading}
                        >
                          <Sparkles className="h-3 w-3 mr-1" />
                          Improve
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* AI Response Output */}
                  {showOutput && aiResponse && (
                    <div className="space-y-2">
                      <Separator />
                      <div className="flex items-center justify-between">
                        <div className="text-xs font-medium text-gray-700">AI Response</div>
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleCopyResponse}
                            className="h-6 w-6 p-0"
                            title="Copy response"
                          >
                            {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setShowOutput(false)}
                            className="h-6 w-6 p-0"
                            title="Hide response"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      <ScrollArea className="max-h-32 border rounded-lg">
                        <div className="p-3 text-xs text-gray-700 whitespace-pre-wrap">
                          {aiResponse}
                        </div>
                      </ScrollArea>
                      <div className="flex gap-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onAIRequest('', aiResponse, 'insert')}
                          className="flex-1 h-7 text-xs"
                        >
                          <Plus className="h-3 w-3 mr-1" />
                          Insert
                        </Button>
                        {selectedText && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onAIRequest('', aiResponse, 'replace')}
                            className="flex-1 h-7 text-xs"
                          >
                            <Replace className="h-3 w-3 mr-1" />
                            Replace
                          </Button>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Tools Tab */}
              {activeTab === 'tools' && (
                <div className="flex-1 flex flex-col p-3 space-y-3">
                  {/* Quick Enhancement Tools */}
                  <div className="space-y-2">
                    <div className="text-xs font-medium text-gray-700 flex items-center gap-2">
                      <Sparkles className="h-3 w-3 text-blue-500" />
                      Quick Enhancements
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      {quickTools.map((tool) => (
                        <Button
                          key={tool.id}
                          variant="outline"
                          size="sm"
                          className={`h-auto p-2 flex flex-col items-center gap-1 text-xs transition-all duration-200 hover:shadow-sm ${
                            tool.requiresSelection && !selectedText.trim() ? 'opacity-50' : ''
                          }`}
                          onClick={() => handleToolExecution(tool)}
                          disabled={aiLoading || processingTool !== null || (tool.requiresSelection && !selectedText.trim())}
                          title={tool.description}
                        >
                          {processingTool === tool.id ? (
                            <Loader2 className="h-3 w-3 animate-spin" />
                          ) : (
                            tool.icon
                          )}
                          <span className="font-medium">{tool.name}</span>
                        </Button>
                      ))}
                    </div>
                  </div>

                  <Separator />

                  {/* Research Tools */}
                  <div className="space-y-2">
                    <div className="text-xs font-medium text-gray-700 flex items-center gap-2">
                      <Search className="h-3 w-3 text-green-500" />
                      Research & Analysis
                    </div>
                    <div className="grid grid-cols-2 gap-2">
                      {researchTools.map((tool) => (
                        <Button
                          key={tool.id}
                          variant="outline"
                          size="sm"
                          className={`h-auto p-2 flex flex-col items-center gap-1 text-xs transition-all duration-200 hover:shadow-sm ${
                            tool.requiresSelection && !selectedText.trim() ? 'opacity-50' : ''
                          }`}
                          onClick={() => handleToolExecution(tool)}
                          disabled={aiLoading || processingTool !== null || (tool.requiresSelection && !selectedText.trim())}
                          title={tool.description}
                        >
                          {processingTool === tool.id ? (
                            <Loader2 className="h-3 w-3 animate-spin" />
                          ) : (
                            tool.icon
                          )}
                          <span className="font-medium">{tool.name}</span>
                        </Button>
                      ))}
                    </div>
                  </div>

                  {selectedText && (
                    <div className="mt-2 p-2 bg-blue-50 rounded-lg">
                      <div className="text-xs text-blue-700 font-medium mb-1">Selected Text</div>
                      <div className="text-xs text-blue-600 line-clamp-2">
                        {selectedText.slice(0, 100)}...
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Research Tab */}
              {activeTab === 'search' && (
                <div className="flex-1 flex flex-col p-3 space-y-3">
                  <div className="space-y-2">
                    <Textarea
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder="Ask a research question and I'll search online for current information..."
                      className="ai-prompt-input min-h-[80px] max-h-[120px] resize-none text-sm"
                      disabled={aiLoading || searchLoading}
                    />

                    <Button
                      onClick={handleAIRequestWithSearch}
                      disabled={!prompt.trim() || aiLoading || searchLoading}
                      className="w-full h-8"
                      size="sm"
                    >
                      {searchLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <Search className="h-4 w-4 mr-2" />
                      )}
                      Research & Answer
                    </Button>
                  </div>

                  {/* Search Results */}
                  {searchResults.length > 0 && (
                    <ScrollArea className="flex-1">
                      <div className="space-y-2">
                        <div className="text-xs font-medium text-gray-700">Sources Found</div>
                        {searchResults.slice(0, 3).map((result, index) => (
                          <div key={index} className="p-2 border rounded text-xs">
                            <div className="font-medium text-gray-900 mb-1">{result.title}</div>
                            <div className="text-gray-600 text-xs line-clamp-2">{result.content}</div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => window.open(result.url, '_blank')}
                              className="h-6 w-6 p-0 mt-1"
                            >
                              <ExternalLink className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  )}
                </div>
              )}

              {/* Settings */}
              <div className="p-3 border-t bg-gray-50">
                <div className="flex items-center justify-between">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowModelSelector(!showModelSelector)}
                    className="h-7 px-2 text-xs"
                  >
                    <Settings className="h-3 w-3 mr-1" />
                    Model
                  </Button>
                  
                  {aiResponse && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleCopyResponse}
                      className="h-7 px-2 text-xs"
                    >
                      {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </>
        )}
      </Card>
    </div>
  );
}
