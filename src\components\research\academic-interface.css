/* Academic Interface Styling for Enhanced Writing Editor */

/* Primary AI Assistant St<PERSON><PERSON> */
.primary-ai-assistant {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.primary-ai-assistant:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.ai-prompt-input {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.ai-prompt-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

/* Action Mode Buttons */
.action-mode-button {
  transition: all 0.2s ease;
  border-radius: 6px;
  font-weight: 500;
}

.action-mode-button.selected {
  background: #3b82f6;
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.action-mode-button:not(.selected) {
  background: white;
  border: 1px solid #e2e8f0;
  color: #64748b;
}

.action-mode-button:not(.selected):hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: #475569;
}

/* Popup Panels Styling */
.popup-panel {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
  backdrop-filter: blur(8px);
}

.popup-panel-header {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-bottom: 1px solid #e2e8f0;
  border-radius: 16px 16px 0 0;
}

.popup-panel-content {
  max-height: calc(85vh - 120px);
  overflow-y: auto;
}

/* Tools Grid Styling */
.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  padding: 24px;
}

.tool-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.tool-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.tool-card:hover::before {
  transform: scaleX(1);
}

.tool-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #cbd5e1;
}

.tool-card.generation {
  border-left: 4px solid #3b82f6;
}

.tool-card.enhancement {
  border-left: 4px solid #10b981;
}

.tool-card.analysis {
  border-left: 4px solid #8b5cf6;
}

.tool-card.review {
  border-left: 4px solid #f59e0b;
}

/* Academic Search Styling */
.search-panel {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.search-controls {
  background: linear-gradient(135deg, #fafbfc 0%, #f4f6f8 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 24px;
  border-radius: 16px 16px 0 0;
}

.search-input-group {
  position: relative;
}

.search-input {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 16px;
  transition: all 0.2s ease;
  width: 100%;
}

.search-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.search-button {
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.2s ease;
  cursor: pointer;
}

.search-button:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.search-button:disabled {
  background: #94a3b8;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Citation Style Selector */
.citation-selector {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px 12px;
  transition: all 0.2s ease;
}

.citation-selector:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

/* Generated Content Display */
.generated-content {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  font-family: 'Georgia', 'Times New Roman', serif;
  line-height: 1.7;
  color: #374151;
}

.generated-content p {
  margin-bottom: 16px;
}

.generated-content p:last-child {
  margin-bottom: 0;
}

/* References Box */
.references-box {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border: 1px solid #bfdbfe;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
}

.references-box h4 {
  color: #1e40af;
  font-weight: 600;
  margin-bottom: 16px;
  font-size: 16px;
}

.reference-item {
  background: white;
  border: 1px solid #e0e7ff;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 1.5;
  color: #374151;
}

.reference-item:last-child {
  margin-bottom: 0;
}

/* Source Links */
.source-links {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
}

.source-link-item {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.source-link-item:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.source-link-item:last-child {
  margin-bottom: 0;
}

.source-link-title {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
  font-size: 14px;
}

.source-link-content {
  color: #6b7280;
  font-size: 13px;
  line-height: 1.4;
}

/* Formatting Toolbar Enhancements */
.formatting-toolbar {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 12px 16px;
  transition: all 0.3s ease;
}

.formatting-toolbar.hidden {
  max-height: 0;
  padding: 0 16px;
  overflow: hidden;
  opacity: 0;
}

.formatting-toolbar.visible {
  max-height: 80px;
  opacity: 1;
}

.toolbar-toggle {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 4px 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.toolbar-toggle:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

/* Floating AI Assistant */
.floating-ai-assistant {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(12px);
  animation: slideInFromBottom 0.3s ease-out;
  max-width: 420px;
  min-width: 380px;
  /* Ensure proper scaling at different zoom levels */
  transform-origin: bottom right;
  /* Prevent content overflow */
  overflow: hidden;
}

.floating-ai-assistant .ai-prompt-input {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 14px;
}

.floating-ai-assistant .ai-prompt-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  background: white;
}

/* Floating Tools Panel */
.floating-tools-panel {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(12px);
  animation: slideInFromCenter 0.3s ease-out;
}

/* Floating AI Toolbar Improvements */
.floating-ai-toolbar {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(8px);
  animation: fadeInUp 0.2s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes slideInFromCenter {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Loading States */
.ai-loading {
  background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Responsive Design and Zoom Handling */
@media (max-width: 768px) {
  .tools-grid {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 16px;
  }

  .tool-card {
    padding: 16px;
  }

  .search-controls {
    padding: 16px;
  }

  .generated-content {
    padding: 16px;
    font-size: 14px;
  }

  .popup-panel {
    margin: 16px;
    border-radius: 12px;
  }

  .floating-ai-assistant {
    max-width: 95vw;
    min-width: 320px;
    bottom: 4px;
    right: 4px;
  }

  .floating-ai-toolbar {
    max-width: 280px;
  }
}

/* Better scaling for different zoom levels */
@media (min-width: 769px) {
  .floating-ai-assistant {
    /* Adjust position based on viewport */
    bottom: min(24px, 2vh);
    right: min(24px, 2vw);
  }
}

/* Ensure proper layout at 100% zoom */
.editor-content {
  /* Prevent content from being too zoomed */
  zoom: 1;
  transform: scale(1);
  transform-origin: top left;
}

/* Improve popup positioning to avoid overlap */
.popup-panel-content {
  max-height: min(calc(85vh - 120px), 600px);
  overflow-y: auto;
}

/* Better floating toolbar positioning */
.floating-ai-toolbar {
  /* Ensure it doesn't go off-screen */
  max-width: min(320px, 90vw);
  /* Better positioning relative to viewport */
  transform: translateY(-100%) translateX(0);
}

/* Ensure AI assistant doesn't overlap with editor */
.floating-ai-assistant {
  /* Add some margin from viewport edges */
  max-height: calc(100vh - 48px);
  /* Ensure it doesn't interfere with editor */
  z-index: 1000;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .primary-ai-assistant {
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border-color: #475569;
  }
  
  .ai-prompt-input {
    background: #1e293b;
    border-color: #475569;
    color: #f1f5f9;
  }
  
  .popup-panel {
    background: #1e293b;
    border-color: #475569;
  }
  
  .generated-content {
    background: #334155;
    border-color: #475569;
    color: #f1f5f9;
  }
}
