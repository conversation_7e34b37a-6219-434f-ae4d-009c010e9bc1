import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import {
  Sparkles,
  CheckCircle,
  PenTool,
  AlignLeft,
  Edit,
  X,
  Loader2,
  Send,
  ChevronDown,
  ChevronUp,
  Wand2
} from 'lucide-react';
import { enhancedAIService } from './paper-generator/enhanced-ai.service';
import './academic-interface.css';
import { toast } from 'sonner';

interface FloatingAIToolbarProps {
  selectedText: string;
  position: { x: number; y: number };
  onApplyResult: (result: string, mode: 'replace' | 'insert') => void;
  onClose: () => void;
  isLoading?: boolean;
  documentContext?: string; // Additional context from the document
  onInteractionStart?: () => void;
  onInteractionEnd?: () => void;
}

export function FloatingAIToolbar({
  selectedText,
  position,
  onApplyResult,
  onClose,
  isLoading = false,
  documentContext = '',
  onInteractionStart,
  onInteractionEnd
}: FloatingAIToolbarProps) {
  const [processingTool, setProcessingTool] = useState<string | null>(null);
  const [showCustomPrompt, setShowCustomPrompt] = useState(false);
  const [customPrompt, setCustomPrompt] = useState('');

  // Quick enhancement tools
  const quickTools = [
    {
      id: 'clarity',
      name: 'Improve',
      icon: <Sparkles className="h-3 w-3" />,
      color: 'blue',
      description: 'Improve clarity and flow'
    },
    {
      id: 'grammar',
      name: 'Grammar',
      icon: <CheckCircle className="h-3 w-3" />,
      color: 'green',
      description: 'Fix grammar and style'
    },
    {
      id: 'academic',
      name: 'Academic',
      icon: <PenTool className="h-3 w-3" />,
      color: 'purple',
      description: 'Convert to academic tone'
    },
    {
      id: 'expand',
      name: 'Expand',
      icon: <AlignLeft className="h-3 w-3" />,
      color: 'indigo',
      description: 'Add more detail'
    }
  ];

  // Essential quick actions only
  const essentialActions = [
    {
      id: 'humanize',
      name: 'Humanize',
      icon: <Wand2 className="h-3 w-3" />,
      color: 'pink',
      description: 'Make text sound more natural'
    }
  ];

  const handleQuickAction = async (toolId: string) => {
    if (!selectedText.trim() || isLoading) return;

    setProcessingTool(toolId);

    try {
      let result: string;

      // Handle quick enhancement tools
      if (['clarity', 'grammar', 'academic', 'expand'].includes(toolId)) {
        result = await enhancedAIService.quickEnhance(
          selectedText,
          toolId as 'clarity' | 'grammar' | 'academic' | 'expand'
        );
      } else if (toolId === 'humanize') {
        result = await enhancedAIService.generateText(
          `Please humanize the following academic text to make it sound more natural and human-written while maintaining academic quality:\n\n"${selectedText}"`
        );
      } else {
        throw new Error(`Unknown tool: ${toolId}`);
      }

      onApplyResult(result, 'replace');
      onClose();

    } catch (error: any) {
      console.error('Quick action failed:', error);
      toast.error(`Failed to ${toolId} text: ${error.message}`);
    } finally {
      setProcessingTool(null);
    }
  };



  const handleCustomPrompt = async () => {
    if (!customPrompt.trim() || !selectedText.trim() || isLoading) return;

    setProcessingTool('custom');

    try {
      const context = documentContext ? `Document context: ${documentContext.slice(0, 500)}...\n\n` : '';
      const result = await enhancedAIService.generateText(
        `${context}Selected text: "${selectedText}"\n\nUser request: ${customPrompt}`
      );

      onApplyResult(result, 'replace');
      setCustomPrompt('');
      onClose();

    } catch (error: any) {
      console.error('Custom prompt failed:', error);
      toast.error(`Failed to process custom prompt: ${error.message}`);
    } finally {
      setProcessingTool(null);
    }
  };

  const getColorClasses = (color: string) => {
    const classes = {
      blue: 'bg-blue-100 hover:bg-blue-200 text-blue-700 border-blue-200',
      green: 'bg-green-100 hover:bg-green-200 text-green-700 border-green-200',
      purple: 'bg-purple-100 hover:bg-purple-200 text-purple-700 border-purple-200',
      indigo: 'bg-indigo-100 hover:bg-indigo-200 text-indigo-700 border-indigo-200',
      emerald: 'bg-emerald-100 hover:bg-emerald-200 text-emerald-700 border-emerald-200',
      orange: 'bg-orange-100 hover:bg-orange-200 text-orange-700 border-orange-200',
      cyan: 'bg-cyan-100 hover:bg-cyan-200 text-cyan-700 border-cyan-200',
      yellow: 'bg-yellow-100 hover:bg-yellow-200 text-yellow-700 border-yellow-200'
    };
    return classes[color as keyof typeof classes] || classes.blue;
  };

  return (
    <Card
      className="floating-ai-toolbar fixed z-50 p-3 shadow-xl border bg-white/95 backdrop-blur-sm"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: 'translateY(-100%)',
        maxWidth: '320px'
      }}
      onMouseEnter={() => {
        onInteractionStart?.();
      }}
      onMouseLeave={(e) => {
        // Only close if mouse leaves AND no active interaction
        const relatedTarget = e.relatedTarget as Element;
        if (!relatedTarget || !e.currentTarget.contains(relatedTarget)) {
          onInteractionEnd?.();
        }
      }}
      onClick={(e) => {
        e.stopPropagation();
      }}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Edit className="h-4 w-4 text-gray-500" />
          <span className="text-sm font-medium text-gray-700">AI Assistant</span>
          <Badge variant="secondary" className="text-xs">
            {selectedText.length} chars
          </Badge>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="h-6 w-6 p-0 hover:bg-gray-100"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Quick Enhancement Tools */}
      <div className="mb-3">
        <div className="text-xs font-medium text-gray-600 mb-2">Quick Enhancements</div>
        <div className="grid grid-cols-2 gap-1.5">
          {quickTools.map((tool) => (
            <Button
              key={tool.id}
              variant="outline"
              size="sm"
              className={`h-auto p-2 flex flex-col items-center gap-1 text-xs transition-all duration-200 ${getColorClasses(tool.color)}`}
              onClick={() => handleQuickAction(tool.id)}
              disabled={isLoading || processingTool !== null}
              title={tool.description}
            >
              {processingTool === tool.id ? (
                <Loader2 className="h-3 w-3 animate-spin" />
              ) : (
                tool.icon
              )}
              <span className="font-medium">{tool.name}</span>
            </Button>
          ))}
        </div>
      </div>

      <Separator className="my-3" />

      {/* Essential Actions */}
      <div className="mb-3">
        <div className="text-xs font-medium text-gray-600 mb-2">More Actions</div>
        <div className="grid grid-cols-1 gap-1.5">
          {essentialActions.map((tool) => (
            <Button
              key={tool.id}
              variant="outline"
              size="sm"
              className={`h-auto p-2 flex items-center gap-2 text-xs transition-all duration-200 ${getColorClasses(tool.color)}`}
              onClick={() => handleQuickAction(tool.id)}
              disabled={isLoading || processingTool !== null}
              title={tool.description}
            >
              {processingTool === tool.id ? (
                <Loader2 className="h-3 w-3 animate-spin" />
              ) : (
                tool.icon
              )}
              <span className="font-medium">{tool.name}</span>
            </Button>
          ))}
        </div>
      </div>

      <Separator className="my-3" />

      {/* Custom Prompt */}
      <div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowCustomPrompt(!showCustomPrompt)}
          className="w-full justify-between text-xs font-medium text-gray-600 h-6 p-1"
        >
          Custom AI Prompt
          {showCustomPrompt ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
        </Button>

        {showCustomPrompt && (
          <div className="mt-2 space-y-2">
            <Textarea
              value={customPrompt}
              onChange={(e) => setCustomPrompt(e.target.value)}
              placeholder="Ask AI to do something specific with this text..."
              className="w-full text-xs min-h-[60px] resize-none"
              disabled={isLoading || processingTool !== null}
            />
            <Button
              size="sm"
              onClick={handleCustomPrompt}
              disabled={!customPrompt.trim() || isLoading || processingTool !== null}
              className="w-full h-7 text-xs"
            >
              {processingTool === 'custom' ? (
                <Loader2 className="h-3 w-3 animate-spin mr-1" />
              ) : (
                <Send className="h-3 w-3 mr-1" />
              )}
              Apply Custom Prompt
            </Button>
          </div>
        )}
      </div>

      <div className="mt-3 text-xs text-gray-500 text-center">
        <div>Quick actions for selected text</div>
        <div className="mt-1 text-blue-600">Open AI Assistant for more tools</div>
      </div>
    </Card>
  );
}
